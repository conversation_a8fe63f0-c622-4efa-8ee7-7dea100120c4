import torch
import torch.nn.functional as F


def calculate_entropy(pred_logit):
    """
    计算预测的熵（信息最大化损失）
    Args:
        pred_logit: 模型预测的logits，尺寸为 [B, C, H, W]
    Returns:
        熵损失值
    """
    # 对logits应用softmax
    pred_prob = torch.softmax(pred_logit, dim=1)
    
    # 计算熵：-sum(p * log(p))
    log_prob = torch.log(pred_prob + 1e-9)  # 添加小常数避免log(0)
    entropy = -torch.sum(pred_prob * log_prob, dim=1)  # [B, H, W]
    
    # 返回平均熵
    return entropy.mean()


def calculate_orthogonal_loss(s_stream, c_stream, model, x):
    """
    计算梯度正交损失
    Args:
        s_stream: S-Stream模块
        c_stream: C-Stream模块
        model: 骨干网络模型
        x: 输入图像
    Returns:
        梯度正交损失值
    """
    # 获取S-Stream和C-Stream的参数
    s_params = list(s_stream.parameters())
    c_params = list(c_stream.parameters())
    
    # 计算S-stream的梯度
    # 只用S-stream处理图像
    x_fft = torch.fft.fft2(x, dim=(-2, -1))
    processed_fft_s = s_stream(x_fft)
    x_s = torch.fft.ifft2(processed_fft_s, dim=(-2, -1)).real
    
    pred_s, _, _ = model(x_s)
    entropy_s = calculate_entropy(pred_s)
    
    # 计算S-stream参数的梯度
    grad_s = torch.autograd.grad(entropy_s, s_params, retain_graph=True, create_graph=True)
    grad_s_flat = torch.cat([g.view(-1) for g in grad_s if g is not None])
    
    # 计算C-stream的梯度
    # 只用C-stream处理图像
    phase = x_fft.angle()
    phase_c = c_stream(phase)
    magnitude = x_fft.abs()
    
    real = magnitude * torch.cos(phase_c)
    imag = magnitude * torch.sin(phase_c)
    x_fft_c = torch.complex(real, imag)
    x_c = torch.fft.ifft2(x_fft_c, dim=(-2, -1)).real
    
    pred_c, _, _ = model(x_c)
    entropy_c = calculate_entropy(pred_c)
    
    # 计算C-stream参数的梯度
    grad_c = torch.autograd.grad(entropy_c, c_params, retain_graph=True, create_graph=True)
    grad_c_flat = torch.cat([g.view(-1) for g in grad_c if g is not None])
    
    # 计算余弦相似度的平方
    if len(grad_s_flat) > 0 and len(grad_c_flat) > 0:
        # 确保两个梯度向量长度相同，通过填充或截断
        min_len = min(len(grad_s_flat), len(grad_c_flat))
        if min_len > 0:
            grad_s_truncated = grad_s_flat[:min_len]
            grad_c_truncated = grad_c_flat[:min_len]
            cosine_sim = F.cosine_similarity(grad_s_truncated.unsqueeze(0), grad_c_truncated.unsqueeze(0), dim=1)
            loss_orth = cosine_sim.pow(2).mean()
        else:
            loss_orth = torch.tensor(0.0, device=x.device)
    else:
        loss_orth = torch.tensor(0.0, device=x.device)
    
    return loss_orth


def calculate_sparse_loss(c_stream):
    """
    计算C-Stream稀疏性损失
    Args:
        c_stream: C-Stream模块
    Returns:
        稀疏性损失值（L1范数）
    """
    return torch.norm(c_stream.phase_conv.weight, p=1)


def calculate_loss_c(dofp_adapter, model, x, pred_logit, lambda_orth=1.0, lambda_sparse=0.1):
    """
    计算完整的Loss_C
    Args:
        dofp_adapter: DOFP适配器
        model: 骨干网络模型
        x: 输入图像
        pred_logit: 模型预测的logits
        lambda_orth: 梯度正交损失权重
        lambda_sparse: 稀疏性损失权重
    Returns:
        总的Loss_C
    """
    # 信息最大化损失
    loss_info = calculate_entropy(pred_logit)
    
    # 梯度正交损失
    loss_orth = calculate_orthogonal_loss(
        dofp_adapter.s_stream, 
        dofp_adapter.c_stream, 
        model, 
        x
    )
    
    # 稀疏性损失
    loss_sparse = calculate_sparse_loss(dofp_adapter.c_stream)
    
    # 组合损失
    loss_c = loss_info + lambda_orth * loss_orth + lambda_sparse * loss_sparse
    
    return loss_c, loss_info, loss_orth, loss_sparse
