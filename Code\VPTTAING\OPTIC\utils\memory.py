import torch
import numpy as np
from numpy.linalg import norm
import pickle


class Memory(object):
    """
        Create the empty memory buffer for DOFP parameters
    """

    def __init__(self, size, dimension=512):  # 修改默认维度为深层特征维度
        self.memory = {}
        self.size = size
        self.dimension = dimension

    def reset(self):
        self.memory = {}

    def get_size(self):
        return len(self.memory)

    def push(self, keys, dofp_params):
        """
        存储DOFP参数到Memory Bank
        Args:
            keys: 深层特征键值 [N, dimension]
            dofp_params: DOFP参数字典列表
        """
        # 确保keys是正确的形状
        if len(keys.shape) == 1:
            keys = keys.reshape(1, -1)

        for i, key in enumerate(keys):
            if len(self.memory.keys()) > self.size:
                self.memory.pop(list(self.memory)[0])

            # 序列化DOFP参数字典
            serialized_params = pickle.dumps(dofp_params[i])
            self.memory.update(
                {key.reshape(self.dimension).tobytes(): serialized_params})

    def _prepare_batch(self, sample, attention_weight):
        """
        对检索到的DOFP参数进行加权融合
        """
        attention_weight = np.array(attention_weight / 0.2)
        attention_weight = np.exp(attention_weight) / (np.sum(np.exp(attention_weight)))

        # 反序列化第一个样本
        first_params = pickle.loads(sample[0])
        ensemble_params = {}

        # 对每个参数进行加权平均
        for param_name in first_params.keys():
            ensemble_params[param_name] = first_params[param_name] * attention_weight[0]
            for i in range(1, len(sample)):
                sample_params = pickle.loads(sample[i])
                ensemble_params[param_name] += sample_params[param_name] * attention_weight[i]

        return ensemble_params

    def get_neighbours(self, keys, k):
        """
        Returns DOFP parameters from buffer using nearest neighbour approach
        """
        samples = []

        # 确保keys是正确的形状
        if len(keys.shape) == 1:
            keys = keys.reshape(1, -1)

        keys = keys.reshape(len(keys), self.dimension)
        total_keys = len(self.memory.keys())

        # 计算所有存储键的总字节数
        key_bytes = len(list(self.memory.keys())[0])
        expected_bytes = self.dimension * 4  # float32 = 4 bytes

        if key_bytes != expected_bytes:
            print(f"Warning: Key size mismatch. Expected {expected_bytes} bytes, got {key_bytes} bytes")
            return [], 0.0

        self.all_keys = np.frombuffer(
            np.asarray(list(self.memory.keys())), dtype=np.float32).reshape(total_keys, self.dimension)

        for key in keys:
            similarity_scores = np.dot(self.all_keys, key.T) / (norm(self.all_keys, axis=1) * norm(key.T))

            K_neighbour_keys = self.all_keys[np.argpartition(similarity_scores, -k)[-k:]]
            neighbours = [self.memory[nkey.tobytes()] for nkey in K_neighbour_keys]

            attention_weight = np.dot(K_neighbour_keys, key.T) / (norm(K_neighbour_keys, axis=1) * norm(key.T))
            batch = self._prepare_batch(neighbours, attention_weight)
            samples.append(batch)

        return samples, np.mean(similarity_scores)
