#!/bin/bash

#Please modify the following roots to yours.
dataset_root=/opt/data/private/zjw/Data/Fundus
model_root=/opt/data/private/zjw/Data/models
path_save_log=/opt/data/private/zjw/VPTTA-main/OPTIC/logs/

#Dataset [RIM_ONE_r3, REFUGE, ORIGA, REF<PERSON>GE_Valid, Drishti_GS]
Source=RIM_ONE_r3

#Optimizer
optimizer=Adam
lr=0.05

#Hyperparameters
memory_size=40
neighbor=16
dofp_lr=0.01
lambda_orth=1.0
lambda_sparse=0.1
warm_n=5

#Command
cd OPTIC
CUDA_VISIBLE_DEVICES=0 python vptta.py \
--dataset_root $dataset_root --model_root $model_root --path_save_log $path_save_log \
--Source_Dataset $Source \
--optimizer $optimizer --lr $lr \
--memory_size $memory_size --neighbor $neighbor --dofp_lr $dofp_lr --lambda_orth $lambda_orth --lambda_sparse $lambda_sparse --warm_n $warm_n