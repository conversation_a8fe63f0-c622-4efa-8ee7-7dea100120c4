import os
import torch
import numpy as np
import argparse, sys, datetime, time
from config import Logger
from torch.autograd import Variable
from utils.convert import AdaBN
from utils.memory import Memory
from utils.dofp_adapter import DOFP_Adapter
from utils.loss_functions import calculate_loss_c
from utils.metrics import calculate_metrics
from utils.debug_monitor import DebugMonitor
from networks.ResUnet_TTA import ResUnet
from torch.utils.data import DataLoader
from dataloaders.OPTIC_dataloader import OPTIC_dataset
from dataloaders.transform import collate_fn_wo_transform
from dataloaders.convert_csv_to_list import convert_labeled_list


torch.set_num_threads(1)


class VPTTA:
    def __init__(self, config):
        # Save Log
        time_now = datetime.datetime.now().__format__("%Y%m%d_%H%M%S_%f")
        log_root = os.path.join(config.path_save_log, 'VPTTA')
        if not os.path.exists(log_root):
            os.makedirs(log_root)
        log_path = os.path.join(log_root, time_now + '.log')
        sys.stdout = Logger(log_path, sys.stdout)

        # Data Loading
        target_test_csv = []
        for target in config.Target_Dataset:
            if target != 'REFUGE_Valid':
                target_test_csv.append(target + '_train.csv')
                target_test_csv.append(target + '_test.csv')
            else:
                target_test_csv.append(target + '.csv')
        ts_img_list, ts_label_list = convert_labeled_list(config.dataset_root, target_test_csv)
        target_test_dataset = OPTIC_dataset(config.dataset_root, ts_img_list, ts_label_list,
                                            config.image_size, img_normalize=True)
        self.target_test_loader = DataLoader(dataset=target_test_dataset,
                                             batch_size=config.batch_size,
                                             shuffle=False,
                                             pin_memory=True,
                                             drop_last=False,
                                             collate_fn=collate_fn_wo_transform,
                                             num_workers=config.num_workers)
        self.image_size = config.image_size

        # Model
        self.load_model = os.path.join(config.model_root, str(config.Source_Dataset))  # Pre-trained Source Model
        self.backbone = config.backbone
        self.in_ch = config.in_ch
        self.out_ch = config.out_ch

        # Optimizer
        self.optim = config.optimizer
        self.lr = config.lr
        self.weight_decay = config.weight_decay
        self.momentum = config.momentum
        self.betas = (config.beta1, config.beta2)

        # GPU
        self.device = config.device

        # Warm-up
        self.warm_n = config.warm_n

        # DOFP parameters
        self.dofp_lr = config.dofp_lr
        self.lambda_orth = config.lambda_orth
        self.lambda_sparse = config.lambda_sparse
        self.iters = config.iters

        # Initialize the pre-trained model
        self.build_model()

        # Memory Bank - 使用深层特征维度
        self.neighbor = config.neighbor
        self.memory_bank = Memory(size=config.memory_size, dimension=32)  # head_input特征维度

        # 初始化调试监控器
        total_samples = len(self.target_test_loader)
        self.debug_monitor = DebugMonitor(total_samples)

        # Print Information
        for arg, value in vars(config).items():
            print(f"{arg}: {value}")
        print('***' * 20)

    def build_model(self):
        # 只初始化骨干网络，DOFP_Adapter将在每个测试样本时创建
        self.model = ResUnet(resnet=self.backbone, num_classes=self.out_ch, pretrained=False, newBN=AdaBN, warm_n=self.warm_n).to(self.device)
        checkpoint = torch.load(os.path.join(self.load_model, 'last-Res_Unet.pth'))
        self.model.load_state_dict(checkpoint, strict=True)

    def extract_memory_features(self, x_adapted):
        """
        使用DOFP适应后的图像提取深层特征作为Memory Bank的键
        """
        with torch.no_grad():
            _, sfs, head_input = self.model(x_adapted)
            # 使用head_input作为特征，它是最终的特征表示
            memory_key = head_input.mean(dim=(2, 3))  # [B, C] -> [B, C]
            return memory_key

    def store_dofp_parameters(self, dofp_adapter):
        """
        将DOFP_Adapter的所有参数打包存储
        """
        params_dict = {}
        for name, param in dofp_adapter.named_parameters():
            params_dict[name] = param.detach().clone()
        return params_dict

    def load_dofp_parameters(self, dofp_adapter, params_dict):
        """
        从存储的参数字典恢复DOFP_Adapter参数
        """
        for name, param in dofp_adapter.named_parameters():
            if name in params_dict:
                param.data.copy_(params_dict[name])

    def run(self):
        metric_dict = ['Disc_Dice', 'Disc_ASD', 'Cup_Dice', 'Cup_ASD']

        # Valid on Target
        metrics_test = [[], [], [], []]

        for batch, data in enumerate(self.target_test_loader):
            x, y = data['data'], data['mask']
            x = torch.from_numpy(x).to(dtype=torch.float32)
            y = torch.from_numpy(y).to(dtype=torch.float32)

            x, y = Variable(x).to(self.device), Variable(y).to(self.device)

            # 1. 为每个测试样本创建新的DOFP_Adapter
            dofp_adapter = DOFP_Adapter().to(self.device)

            # 2. Memory Bank初始化
            if len(self.memory_bank.memory.keys()) >= self.neighbor:
                # 先用默认参数进行一次前向传播获取特征
                with torch.no_grad():
                    x_temp = dofp_adapter(x)
                    memory_key = self.extract_memory_features(x_temp)

                # 检索相似的DOFP参数进行初始化
                init_params_list, score = self.memory_bank.get_neighbours(
                    keys=memory_key.cpu().numpy(), k=self.neighbor
                )
                if init_params_list:
                    self.load_dofp_parameters(dofp_adapter, init_params_list[0])

            # 3. 创建优化器
            optimizer = torch.optim.Adam(dofp_adapter.parameters(), lr=self.dofp_lr)

            # 4. 设置模型状态
            self.model.eval()
            dofp_adapter.train()
            self.model.change_BN_status(new_sample=True)

            # 5. 单步优化
            for tr_iter in range(self.iters):
                optimizer.zero_grad()

                # 前向传播
                x_adapted = dofp_adapter(x)
                pred_logit, features, _ = self.model(x_adapted)

                # 计算Loss_S (BN损失)
                times, bn_loss = 0, 0
                for nm, m in self.model.named_modules():
                    if isinstance(m, AdaBN):
                        bn_loss += m.bn_loss
                        times += 1
                loss_s = bn_loss / times

                # 计算Loss_C
                loss_c, loss_info, loss_orth, loss_sparse = calculate_loss_c(
                    dofp_adapter, self.model, x, pred_logit,
                    self.lambda_orth, self.lambda_sparse
                )

                # 总损失
                total_loss = loss_s + loss_c

                # 反向传播
                total_loss.backward()
                optimizer.step()
                self.model.change_BN_status(new_sample=False)

            # 6. 最终推理
            self.model.eval()
            dofp_adapter.eval()
            with torch.no_grad():
                x_final = dofp_adapter(x)
                final_pred, final_features, _ = self.model(x_final)

                # 提取最终特征并更新Memory Bank
                memory_key = self.extract_memory_features(x_final)
                dofp_params = self.store_dofp_parameters(dofp_adapter)
                self.memory_bank.push(
                    keys=memory_key.cpu().numpy(),
                    dofp_params=[dofp_params]
                )

            # 7. 计算评估指标
            seg_output = torch.sigmoid(final_pred)
            metrics = calculate_metrics(seg_output.detach().cpu(), y.detach().cpu())
            for i in range(len(metrics)):
                assert isinstance(metrics[i], list), "The metrics value is not list type."
                metrics_test[i] += metrics[i]

        test_metrics_y = np.mean(metrics_test, axis=1)
        print_test_metric_mean = {}
        for i in range(len(test_metrics_y)):
            print_test_metric_mean[metric_dict[i]] = test_metrics_y[i]
        print("Test Metrics: ", print_test_metric_mean)
        print('Mean Dice:', (print_test_metric_mean['Disc_Dice'] + print_test_metric_mean['Cup_Dice']) / 2)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    # Dataset
    parser.add_argument('--Source_Dataset', type=str, default='RIM_ONE_r3',
                        help='RIM_ONE_r3/REFUGE/ORIGA/REFUGE_Valid/Drishti_GS')
    parser.add_argument('--Target_Dataset', type=list)

    parser.add_argument('--num_workers', type=int, default=8)
    parser.add_argument('--image_size', type=int, default=512)

    # Model
    parser.add_argument('--backbone', type=str, default='resnet34', help='resnet34/resnet50')
    parser.add_argument('--in_ch', type=int, default=3)
    parser.add_argument('--out_ch', type=int, default=2)

    # Optimizer
    parser.add_argument('--optimizer', type=str, default='Adam', help='SGD/Adam')
    parser.add_argument('--lr', type=float, default=0.05)
    parser.add_argument('--momentum', type=float, default=0.99)  # momentum in SGD
    parser.add_argument('--beta1', type=float, default=0.9)      # beta1 in Adam
    parser.add_argument('--beta2', type=float, default=0.99)     # beta2 in Adam.
    parser.add_argument('--weight_decay', type=float, default=0.00)

    # Training
    parser.add_argument('--batch_size', type=int, default=1)
    parser.add_argument('--iters', type=int, default=1)

    # Hyperparameters in memory bank, DOFP, and warm-up statistics
    parser.add_argument('--memory_size', type=int, default=40)
    parser.add_argument('--neighbor', type=int, default=16)
    parser.add_argument('--dofp_lr', type=float, default=0.01)
    parser.add_argument('--lambda_orth', type=float, default=1.0)
    parser.add_argument('--lambda_sparse', type=float, default=0.1)
    parser.add_argument('--warm_n', type=int, default=5)

    # Path
    parser.add_argument('--path_save_log', type=str, default='./logs')
    parser.add_argument('--model_root', type=str, default='./models')
    parser.add_argument('--dataset_root', type=str, default='/media/userdisk0/zychen/Datasets/Fundus')

    # Cuda (default: the first available device)
    parser.add_argument('--device', type=str, default='cuda:0')

    config = parser.parse_args()

    config.Target_Dataset = ['RIM_ONE_r3', 'REFUGE', 'ORIGA', 'REFUGE_Valid', 'Drishti_GS']
    config.Target_Dataset.remove(config.Source_Dataset)

    TTA = VPTTA(config)
    TTA.run()
