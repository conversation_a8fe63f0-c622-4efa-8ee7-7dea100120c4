import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class StyleSuppressionPrompt(nn.Module):
    """S-Stream: 可学习的频率带阻滤波器"""
    
    def __init__(self):
        super().__init__()
        # 中心频率 (归一化到-1, 1)，初始化为0
        self.center_frequency = nn.Parameter(torch.zeros(2))
        # 带宽 (用log域保证为正)，初始化为log(1)
        self.log_bandwidth = nn.Parameter(torch.zeros(2))
        # 抑制深度 (0到1之间)，初始化为0.5
        self.depth = nn.Parameter(torch.tensor(0.5))
    
    def forward(self, fft_image):
        """
        Args:
            fft_image: 频域表示，尺寸为 [1, C, H, W]
        Returns:
            修改后的频域表示
        """
        B, C, H, W = fft_image.shape
        device = fft_image.device
        
        # 创建频率网格
        freq_h = torch.linspace(-1, 1, H, device=device)
        freq_w = torch.linspace(-1, 1, W, device=device)
        freq_grid_h, freq_grid_w = torch.meshgrid(freq_h, freq_w)
        
        # 计算到中心频率的距离
        center_h, center_w = self.center_frequency[0], self.center_frequency[1]
        dist_h = (freq_grid_h - center_h) ** 2
        dist_w = (freq_grid_w - center_w) ** 2
        
        # 计算带宽
        bandwidth_h = torch.exp(self.log_bandwidth[0])
        bandwidth_w = torch.exp(self.log_bandwidth[1])
        
        # 生成二维高斯形态的带阻滤波器模板
        gaussian_mask = torch.exp(-(dist_h / (2 * bandwidth_h ** 2) + dist_w / (2 * bandwidth_w ** 2)))
        
        # 使用sigmoid约束深度在(0,1)之间
        sigmoid_depth = torch.sigmoid(self.depth)
        
        # 创建滤波器掩码：在中心频率处值最低，远离时值趋近于1
        filter_mask = 1 - sigmoid_depth * gaussian_mask
        
        # 应用滤波器到幅度谱
        magnitude = fft_image.abs()
        phase = fft_image.angle()
        
        # 将filter_mask扩展到所有批次和通道
        filter_mask = filter_mask.unsqueeze(0).unsqueeze(0).expand(B, C, -1, -1)
        
        # 应用滤波器
        filtered_magnitude = magnitude * filter_mask
        
        # 重组复数
        real = filtered_magnitude * torch.cos(phase)
        imag = filtered_magnitude * torch.sin(phase)
        filtered_fft = torch.complex(real, imag)
        
        return filtered_fft


class ContentEnhancementPrompt(nn.Module):
    """C-Stream: 可学习的稀疏卷积相位滤波器"""
    
    def __init__(self):
        super().__init__()
        # 极小的卷积层，对每个通道的相位谱独立进行卷积
        self.phase_conv = nn.Conv2d(in_channels=1, out_channels=1, kernel_size=3, padding=1, bias=False)
    
    def forward(self, phase):
        """
        Args:
            phase: 相位谱，尺寸为 [1, C, H, W]
        Returns:
            修改后的相位谱
        """
        B, C, H, W = phase.shape
        processed_phases = []
        
        # 对每个通道的相位独立进行卷积
        for c in range(C):
            channel_phase = phase[:, c:c+1, :, :]  # [1, 1, H, W]
            processed_phase = self.phase_conv(channel_phase)
            processed_phases.append(processed_phase)
        
        # 重新堆叠所有通道
        processed_phase = torch.cat(processed_phases, dim=1)
        
        return processed_phase


class DOFP_Adapter(nn.Module):
    """双流正交频率提示 (DOFP) 适配器"""
    
    def __init__(self):
        super().__init__()
        self.s_stream = StyleSuppressionPrompt()
        self.c_stream = ContentEnhancementPrompt()
    
    def forward(self, x):
        """
        完整的DOFP适应流程
        Args:
            x: 输入图像，尺寸为 [1, C, H, W]
        Returns:
            适应后的图像
        """
        # FFT变换
        x_fft = torch.fft.fft2(x, dim=(-2, -1))
        
        # 分离幅度和相位
        magnitude = x_fft.abs()
        phase = x_fft.angle()
        
        # 并行路径处理
        # S-Stream处理幅度谱
        processed_fft_s = self.s_stream(x_fft)
        mag_s = processed_fft_s.abs()
        
        # C-Stream处理相位谱
        phase_c = self.c_stream(phase)
        
        # 融合：将处理后的幅度和相位重新组合
        real = mag_s * torch.cos(phase_c)
        imag = mag_s * torch.sin(phase_c)
        x_fft_adapted = torch.complex(real, imag)
        
        # IFFT变换回时域
        x_adapted = torch.fft.ifft2(x_fft_adapted, dim=(-2, -1)).real
        
        return x_adapted
