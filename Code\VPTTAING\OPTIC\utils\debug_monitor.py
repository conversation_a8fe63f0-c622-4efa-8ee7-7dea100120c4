import time
import torch
import numpy as np
from typing import Dict, List, Optional, Tuple


class DebugMonitor:
    """DOFP系统调试监控器"""
    
    def __init__(self, total_samples: int):
        self.total_samples = total_samples
        self.current_sample = 0
        self.start_time = time.time()
        self.sample_start_time = 0
        
        # 累积统计
        self.all_metrics = []
        self.all_losses = []
        self.all_timings = []
        self.anomaly_samples = []
        
        # 当前样本状态
        self.current_sample_info = {}
        
        print("=" * 80)
        print(f"🚀 DOFP调试监控启动 - 总样本数: {total_samples}")
        print("=" * 80)
    
    def log_sample_start(self, sample_idx: int, sample_info: Dict = None):
        """记录样本开始处理"""
        self.current_sample = sample_idx + 1
        self.sample_start_time = time.time()
        self.current_sample_info = {
            'idx': sample_idx,
            'timings': {},
            'dofp_params': {},
            'losses': {},
            'metrics': {},
            'memory_status': {},
            'anomalies': []
        }
        
        print(f"\n📋 样本 {self.current_sample:03d}/{self.total_samples:03d} 开始处理...")
    
    def log_dofp_params(self, dofp_adapter, stage: str):
        """记录DOFP参数状态"""
        params_info = {}
        
        # S-Stream参数
        s_stream = dofp_adapter.s_stream
        center_freq = s_stream.center_frequency.detach().cpu().numpy()
        log_bandwidth = s_stream.log_bandwidth.detach().cpu().numpy()
        depth = torch.sigmoid(s_stream.depth).detach().cpu().item()
        
        params_info['s_stream'] = {
            'center_freq': center_freq,
            'bandwidth': np.exp(log_bandwidth),
            'depth': depth
        }
        
        # C-Stream参数
        c_stream = dofp_adapter.c_stream
        weight = c_stream.phase_conv.weight.detach().cpu().numpy()
        weight_norm = np.linalg.norm(weight)
        weight_mean = np.mean(np.abs(weight))
        
        params_info['c_stream'] = {
            'weight_norm': weight_norm,
            'weight_mean': weight_mean,
            'weight_std': np.std(weight)
        }
        
        self.current_sample_info['dofp_params'][stage] = params_info
    
    def log_losses(self, loss_s: float, loss_c: float, loss_info: float, 
                   loss_orth: float, loss_sparse: float, total_loss: float):
        """记录损失信息"""
        losses = {
            'loss_s': loss_s,
            'loss_c': loss_c,
            'loss_info': loss_info,
            'loss_orth': loss_orth,
            'loss_sparse': loss_sparse,
            'total_loss': total_loss
        }
        self.current_sample_info['losses'] = losses
        self.all_losses.append(losses)
    
    def log_memory_bank_status(self, memory_bank, retrieval_info: Dict = None):
        """记录Memory Bank状态"""
        status = {
            'size': memory_bank.get_size(),
            'capacity': memory_bank.size,
            'utilization': memory_bank.get_size() / memory_bank.size,
            'retrieval_info': retrieval_info or {}
        }
        self.current_sample_info['memory_status'] = status
    
    def log_performance(self, metrics: List, stage: str):
        """记录性能指标"""
        # metrics = [disc_dice, disc_asd, cup_dice, cup_asd]
        performance = {
            'disc_dice': np.mean(metrics[0]) if metrics[0] else 0.0,
            'disc_asd': np.mean(metrics[1]) if metrics[1] else 0.0,
            'cup_dice': np.mean(metrics[2]) if metrics[2] else 0.0,
            'cup_asd': np.mean(metrics[3]) if metrics[3] else 0.0
        }
        performance['mean_dice'] = (performance['disc_dice'] + performance['cup_dice']) / 2
        
        self.current_sample_info['metrics'][stage] = performance
        if stage == 'final':
            self.all_metrics.append(performance)
    
    def log_timing(self, stage: str, duration: float):
        """记录时间统计"""
        self.current_sample_info['timings'][stage] = duration
    
    def log_convergence(self, gradients: Dict, param_updates: Dict):
        """记录收敛状态"""
        convergence_info = {
            'grad_norm': sum(torch.norm(g).item() for g in gradients.values() if g is not None),
            'param_update_norm': sum(torch.norm(p).item() for p in param_updates.values()),
            'max_grad': max(torch.max(torch.abs(g)).item() for g in gradients.values() if g is not None),
        }
        self.current_sample_info['convergence'] = convergence_info
    
    def detect_anomaly(self, metrics: Dict, losses: Dict):
        """检测异常情况"""
        anomalies = []
        
        # 性能异常检测
        if metrics.get('mean_dice', 0) < 0.3:
            anomalies.append("低Dice分数")
        
        # 损失异常检测
        if losses.get('total_loss', 0) > 10.0:
            anomalies.append("损失过高")
        
        if np.isnan(losses.get('total_loss', 0)):
            anomalies.append("损失为NaN")
        
        self.current_sample_info['anomalies'] = anomalies
        if anomalies:
            self.anomaly_samples.append(self.current_sample)
        
        return anomalies

    def log_sample_complete(self):
        """记录样本处理完成"""
        total_time = time.time() - self.sample_start_time
        self.log_timing('total', total_time)
        self.all_timings.append(total_time)

        # 输出样本详细信息
        self._print_sample_summary()

    def _print_sample_summary(self):
        """打印样本摘要信息"""
        info = self.current_sample_info

        # 基本信息
        print(f"📊 样本 {self.current_sample:03d} 处理完成:")

        # 性能指标
        if 'final' in info['metrics']:
            metrics = info['metrics']['final']
            print(f"   性能: Disc_Dice={metrics['disc_dice']:.3f} Cup_Dice={metrics['cup_dice']:.3f} "
                  f"Disc_ASD={metrics['disc_asd']:.2f} Cup_ASD={metrics['cup_asd']:.2f} "
                  f"Mean_Dice={metrics['mean_dice']:.3f}")

        # DOFP参数
        if 'final' in info['dofp_params']:
            dofp = info['dofp_params']['final']
            s_params = dofp['s_stream']
            c_params = dofp['c_stream']
            print(f"   DOFP: S[freq=({s_params['center_freq'][0]:.2f},{s_params['center_freq'][1]:.2f}) "
                  f"bw=({s_params['bandwidth'][0]:.2f},{s_params['bandwidth'][1]:.2f}) "
                  f"depth={s_params['depth']:.2f}] C[norm={c_params['weight_norm']:.3f}]")

        # 损失信息
        if info['losses']:
            losses = info['losses']
            print(f"   损失: S={losses['loss_s']:.3f} C={losses['loss_c']:.3f}"
                  f"[info={losses['loss_info']:.3f} orth={losses['loss_orth']:.3f} "
                  f"sparse={losses['loss_sparse']:.3f}] Total={losses['total_loss']:.3f}")

        # Memory Bank状态
        if info['memory_status']:
            mem = info['memory_status']
            retrieval = mem.get('retrieval_info', {})
            source = "MemoryBank" if retrieval.get('used', False) else "Random"
            score = retrieval.get('score', 0.0)
            print(f"   Memory: Size={mem['size']}/{mem['capacity']} "
                  f"Util={mem['utilization']:.1%} Source={source} Score={score:.3f}")

        # 时间统计
        if info['timings']:
            timings = info['timings']
            adapt_time = timings.get('adapt', 0)
            infer_time = timings.get('infer', 0)
            total_time = timings.get('total', 0)
            print(f"   时间: Adapt={adapt_time:.2f}s Infer={infer_time:.2f}s Total={total_time:.2f}s")

        # 异常检测
        if info['anomalies']:
            print(f"   ⚠️  异常: {', '.join(info['anomalies'])}")
        else:
            print(f"   ✅ 状态: 正常")

    def print_final_summary(self, all_metrics: List):
        """打印最终摘要"""
        total_time = time.time() - self.start_time

        print("\n" + "=" * 80)
        print("📈 DOFP系统运行摘要")
        print("=" * 80)

        # 整体统计
        if self.all_metrics:
            metrics_array = np.array([[m['disc_dice'], m['disc_asd'], m['cup_dice'], m['cup_asd'], m['mean_dice']]
                                     for m in self.all_metrics])
            mean_metrics = np.mean(metrics_array, axis=0)
            std_metrics = np.std(metrics_array, axis=0)

            print(f"📊 性能统计:")
            print(f"   Disc_Dice: {mean_metrics[0]:.3f}±{std_metrics[0]:.3f}")
            print(f"   Cup_Dice:  {mean_metrics[2]:.3f}±{std_metrics[2]:.3f}")
            print(f"   Mean_Dice: {mean_metrics[4]:.3f}±{std_metrics[4]:.3f}")
            print(f"   Disc_ASD:  {mean_metrics[1]:.2f}±{std_metrics[1]:.2f}")
            print(f"   Cup_ASD:   {mean_metrics[3]:.2f}±{std_metrics[3]:.2f}")

        # 损失统计
        if self.all_losses:
            loss_keys = ['loss_s', 'loss_c', 'total_loss']
            for key in loss_keys:
                values = [l[key] for l in self.all_losses]
                print(f"   {key}: {np.mean(values):.3f}±{np.std(values):.3f}")

        # 时间统计
        if self.all_timings:
            avg_time = np.mean(self.all_timings)
            total_samples = len(self.all_timings)
            print(f"⏱️  时间统计:")
            print(f"   平均每样本: {avg_time:.2f}s")
            print(f"   总处理时间: {total_time:.1f}s")
            print(f"   处理速度: {total_samples/total_time:.2f} samples/s")

        # 异常统计
        if self.anomaly_samples:
            print(f"⚠️  异常样本: {len(self.anomaly_samples)}个 ({len(self.anomaly_samples)/self.total_samples:.1%})")
            print(f"   异常样本编号: {self.anomaly_samples}")
        else:
            print(f"✅ 异常检测: 无异常样本")

        print("=" * 80)
